package com.ideal.ieai.server.repository.topo.acttimecalculate;

import com.ideal.ieai.server.repository.topo.sequence.GanttBean;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试ActTimeCalculateManager中时间计算逻辑的修复
 */
@RunWith(MockitoJUnitRunner.class)
public class ActTimeCalculateManagerTest {

    private ActTimeCalculateManager manager;
    
    @Before
    public void setUp() {
        manager = new ActTimeCalculateManager();
    }

    /**
     * 测试calculateMaxParentEndTime方法
     */
    @Test
    public void testCalculateMaxParentEndTime() throws Exception {
        // 准备测试数据
        List<String> listParent = Arrays.asList("parent1", "parent2", "parent3");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        // 创建父节点
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime("1000");
        mapAct.put("parent1", parent1);
        
        GanttBean parent2 = new GanttBean();
        parent2.setId("parent2");
        parent2.setExpEndTime("1500");
        mapAct.put("parent2", parent2);
        
        GanttBean parent3 = new GanttBean();
        parent3.setId("parent3");
        parent3.setExpEndTime("800");
        mapAct.put("parent3", parent3);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "calculateMaxParentEndTime", List.class, Map.class);
        method.setAccessible(true);
        
        long result = (Long) method.invoke(manager, listParent, mapAct);
        
        // 验证结果应该是最大的结束时间
        assertEquals(1500L, result);
    }

    /**
     * 测试validateAndFixStartTime方法
     */
    @Test
    public void testValidateAndFixStartTime() throws Exception {
        // 准备测试数据
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("500");  // 开始时间小于父节点结束时间
        ganttBean.setExpEndTime("800");    // 持续时间300
        
        List<String> listParent = Arrays.asList("parent1");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime("1000");  // 父节点结束时间大于子节点开始时间
        mapAct.put("parent1", parent1);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // 验证开始时间被调整为父节点的结束时间
        assertEquals("1000", ganttBean.getExpStartTime());
        // 验证结束时间也相应调整，保持持续时间不变
        assertEquals("1300", ganttBean.getExpEndTime());
    }

    /**
     * 测试当开始时间已经大于父节点结束时间时，不进行调整
     */
    @Test
    public void testValidateAndFixStartTimeNoAdjustmentNeeded() throws Exception {
        // 准备测试数据
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("1200");  // 开始时间大于父节点结束时间
        ganttBean.setExpEndTime("1500");
        
        List<String> listParent = Arrays.asList("parent1");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime("1000");
        mapAct.put("parent1", parent1);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // 验证时间没有被调整
        assertEquals("1200", ganttBean.getExpStartTime());
        assertEquals("1500", ganttBean.getExpEndTime());
    }

    /**
     * 测试多个父节点的情况
     */
    @Test
    public void testValidateAndFixStartTimeMultipleParents() throws Exception {
        // 准备测试数据
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("500");
        ganttBean.setExpEndTime("800");
        
        List<String> listParent = Arrays.asList("parent1", "parent2", "parent3");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime("900");
        mapAct.put("parent1", parent1);
        
        GanttBean parent2 = new GanttBean();
        parent2.setId("parent2");
        parent2.setExpEndTime("1200");  // 最大的结束时间
        mapAct.put("parent2", parent2);
        
        GanttBean parent3 = new GanttBean();
        parent3.setId("parent3");
        parent3.setExpEndTime("700");
        mapAct.put("parent3", parent3);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // 验证开始时间被调整为最大的父节点结束时间
        assertEquals("1200", ganttBean.getExpStartTime());
        assertEquals("1500", ganttBean.getExpEndTime());
    }

    /**
     * 测试空父节点列表的情况
     */
    @Test
    public void testValidateAndFixStartTimeEmptyParents() throws Exception {
        // 准备测试数据
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("500");
        ganttBean.setExpEndTime("800");
        
        List<String> listParent = new ArrayList<>();
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // 验证时间没有被调整
        assertEquals("500", ganttBean.getExpStartTime());
        assertEquals("800", ganttBean.getExpEndTime());
    }

    /**
     * 测试父节点结束时间为null或空字符串的情况
     */
    @Test
    public void testValidateAndFixStartTimeNullParentEndTime() throws Exception {
        // 准备测试数据
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("500");
        ganttBean.setExpEndTime("800");
        
        List<String> listParent = Arrays.asList("parent1", "parent2");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime(null);  // null结束时间
        mapAct.put("parent1", parent1);
        
        GanttBean parent2 = new GanttBean();
        parent2.setId("parent2");
        parent2.setExpEndTime("");    // 空字符串结束时间
        mapAct.put("parent2", parent2);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // 验证时间没有被调整
        assertEquals("500", ganttBean.getExpStartTime());
        assertEquals("800", ganttBean.getExpEndTime());
    }
}
