package com.ideal.ieai.server.repository.topo.acttimecalculate;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.topo.acttimeck.ActTimeCk;
import com.ideal.ieai.server.repository.topo.acttimeck.ActTimeModel;
import com.ideal.ieai.server.repository.topo.avgtime.AvgTimeList;
import com.ideal.ieai.server.repository.topo.avgtime.AvgTimeManager;
import com.ideal.ieai.server.repository.topo.batchtimeoffset.BatchTimeOffsetManager;
import com.ideal.ieai.server.repository.topo.common.GetDataDateManager;
import com.ideal.ieai.server.repository.topo.sequence.*;
import com.ideal.ieai.server.repository.topo.topoAbschlussShow.TopoAbschlussManager;
import com.ideal.ieai.server.repository.topo.watcher.WatcherManager;
import com.ideal.ieai.server.toposerver.common.analyze.MainFlowCfg;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class ActTimeCalculateManager
{
    // CREATE TABLE IEAI_TOPO_ANALY_MODEL(
    // IID DECIMAL(19,0) NOT NULL,
    // IPRJNAME VARCHAR(255),
    // ISYSNAME VARCHAR(255),
    // IINSTANCENAME VARCHAR(255),
    // ISUBSYSNAME VARCHAR(255),
    // IANALYTIME NUMBER(19),
    // CONSTRAINT PK_IEAI_TOPO_ANALY_MODEL PRIMARY KEY (IID)
    // )
    private static final Logger _log = Logger.getLogger(ActTimeCalculateManager.class);
    private static ActTimeCalculateManager intance = null;
    private static int execFrequency = 0;

    /**
     * <li>Description:????????</li>
     *
     * <AUTHOR> @return return ActTimeCalculateManager
     */
    public static ActTimeCalculateManager getInstance ()
    {
        if (intance == null)
        {
            intance = new ActTimeCalculateManager();
        }
        return intance;

    }

    /**
     * 0,???
     * 
     * @param state
     * @param con
     * @return
     * @throws IOException 
     * @throws SQLException 
     * @throws Exception
     */
    public boolean getOperactionState ( int state, Connection con ) throws DBException, IOException, SQLException
    {
        long analyWaitTime = ServerEnv.getInstance().getLongConfig(ServerEnv.TOPO_ANALY_WAITTIME,
            ServerEnv.TOPO_ANALY_WAITTIME_DEFAULT);
        boolean flag = false;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ConfigReader cr = ConfigReader.getInstance();
            cr.init();
            String sql = " select a.ISTATE,a.IRUNTIMES,FUN_GET_DATE_NUMBER_NEW(" + Constants.getCurrentSysDate()
                    + ",8) RUNTIMES  from IEAI_TOPO_PANALY_SIGN a";
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                _log.info("PANALY_SIGN:" + rs.getString("ISTATE"));
                if (state == rs.getLong("ISTATE"))
                {
                    flag = true;
                } else
                {
                    long sysTimes = rs.getLong("RUNTIMES");
                    long oldTimes = rs.getLong("IRUNTIMES");
                    if (sysTimes - oldTimes > analyWaitTime * 1000)
                    {
                        setOperactionState();
                        flag = true;
                    }
                }
            }
        } catch (DBException e)
        {
            _log.error(e);
            throw e;
        } catch (IOException e)
        {
            _log.error(e);
            throw e;
        } catch (SQLException e)
        {
            _log.error(e);
            throw e;
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return flag;
    }

    public void setOperactionState ( int state ) throws SQLException, DBException
    {
        PreparedStatement ps = null;
        Connection con = null;
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            String sql = " update IEAI_TOPO_PANALY_SIGN set ISTATE=? ,IRUNTIMES=FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8)";
            ps = con.prepareStatement(sql);
            ps.setInt(1, state);
            ps.executeUpdate();
            con.commit();
        } catch (SQLException e)
        {
            _log.error(e);
            throw e;
        } catch (DBException e)
        {
            _log.error(e);
            throw e;
        } finally
        {
            DBResource.closePSConn(con, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
    }

    public void setOperactionState () throws SQLException, DBException
    {
        PreparedStatement ps = null;
        Connection con = null;
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            String sql = "update IEAI_TOPO_PANALY_SIGN set ISTATE=0,IRUNTIMES=FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8)  WHERE ISTATE IN (1,2)";
            ps = con.prepareStatement(sql);
            ps.executeUpdate();
            con.commit();
        } catch (SQLException e)
        {
            _log.error(e);
            throw e;
        } catch (DBException e)
        {
            _log.error(e);
            throw e;
        } finally
        {
            DBResource.closePSConn(con, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
    }

    public void operation ( Connection con, List actList, List actWarnList, String dataDate, int i, boolean flag,
            long nowTime ) throws Exception
    {
        try
        {
            long a0 = System.currentTimeMillis();
            ConfigReader cf = ConfigReader.getInstance();
            cf.init();
            boolean s = Boolean.parseBoolean(cf.getProperties(Environment.TOPO_YEAR_LAST_SWITCH, "false"));
            _log.info("??????????:" + dataDate + " topo.year.last.switch=" + s);
            if (s)
            {
                if (i == 0 && SequenceManager.getInstance().getAbsActCountByDateDate(dataDate, con) != 0)
                {
                    TopoAbschlussManager.getInstance().insertSubCommand(dataDate, 0l, con);
                } else
                {
                    TopoAbschlussManager.getInstance().insertSubCommand(dataDate, 1l, con);
                }
            }
            long fl = getGantFlagByDatadate();
            long exists = SequenceManager.getInstance().getActCountByDateDate(dataDate, con);
            if (exists != 0)
            {
                fl = 0;
                //SequenceManager.getInstance().getRuleOvertimebyDate(dataDate, 0l, con);
                List<GanttBean> list = SequenceManager.getInstance().updateBeforeActTopoInstanceList(dataDate, 0l, con);
                SequenceManager.getInstance().updateGantData(dataDate, fl, con);
                SequenceManager.getInstance().updateActTopoInstanceData(dataDate, 0l, con, list);
            } else
            {
                SequenceManager.getInstance().updateGantData(dataDate, 1l, con);
            }
            //??????????????? ---start
            boolean formapSwitch= ServerEnv.getInstance().getBooleanConfigNew2(Environment.QL_OVER_TIME_FORMAP_SWITCH, false);
            if(formapSwitch) {
                SequenceManager.getInstance().getRuleOvertimebyDate(dataDate, con);
            }
            //??????????????? ---end
            // ???????topo???????
            con.commit();
            long a1 = System.currentTimeMillis();
            _log.info("????????:" + dataDate + " proc==" + (a1 - a0));
            // ????????
            Map mapAct = new HashMap();
            // ????????????? dependencyMap
            Map<String, List<String>> dependencyMap = new HashMap<>();
            // ????????????????????
            Map<String, Integer> retryCountMap = new HashMap<>();
            final int MAX_RETRY_COUNT = 5; // ??????????

            List list = new ArrayList();
            List<GanttBean> listSon = new ArrayList();
            long realRunTime = 0;
            long realTime = 0;
            SequenceManager.getInstance().getUUIDMainAct(dataDate, list, con);

            // ????????????? add by wangnan -----start-----
            // ?????????????
            List avgTimeList = new ArrayList();
            boolean avgTimeWarnningSwitch = ServerEnv.getInstance().getBooleanConfig("avgTimeWarnningSwitch", false);
            if (avgTimeWarnningSwitch)
            {
                avgTimeList = AvgTimeManager.getInstance().getAllAvgTimeByNullDayWeekMonth();
            }
            // ????????????? add by wangnan -----end-----

            while (true)
            {
                while (!list.isEmpty())
                {

                    GanttBean ganttBean = (GanttBean) list.remove(0);
                    // ????????????????????

                    GanttValTimeBean ganttValTimeBean = SequenceManager.getInstance().getActValTime(ganttBean, con);
                    GanttDelayerBean ganttDelayerBean = SequenceManager.getInstance().getActDelayer(ganttBean, con);

                    // ?????????
//                    List<SequenceTime> srtlist = SequenceManager.getInstance().getActRealRunTime(ganttBean, con);
                    // ?????????
                    List<SequenceTime> srtlist = SequenceManager.getInstance().getActRealRunTimeTwo(ganttBean, con);

                    for (int j = 0; j < srtlist.size(); j++)
                    {

                        if (srtlist.get(j).getType() == 0)
                        {
                            realTime = srtlist.get(j).getTime();
                        } else
                        {
                            realRunTime = srtlist.get(j).getTime();
                        }
                    }

                    if(srtlist.isEmpty()){
                        realRunTime = 0;
                    }

                    // //???????
                    // long realTime =
                    // SequenceManager.getInstance().getActRealRunTime(
                    // ganttBean, con);
                    // //??????
                    // long realRunTime =
                    // SequenceManager.getInstance().getActRealRunTime(
                    // ganttBean, con);

                    // ?????????????????????????????????????????
                    ActTimeCk atc = new ActTimeCk();
                    ActTimeModel atm = atc.queryActTimeCkCal(con, ganttBean.getProjectName(), ganttBean.getActName(),
                        dataDate);

                    List listParent = new ArrayList();
                    SequenceManager.getInstance().getParentId(ganttBean, listParent, dataDate, con);
                    // ?????????????
                    SequenceManager.getInstance().getProOutParentId(ganttBean, listParent, con);

                    // ????????????????????
                    ensureCrossSystemParentsCalculated(ganttBean, listParent, mapAct, dataDate, con);

                    // ??鸸???????????? - ???????
                    if (!isExsitAct(listParent, mapAct))
                    {
                        // ??????????????????????
                        String nodeId = ganttBean.getId();
                        int retryCount = retryCountMap.getOrDefault(nodeId, 0);
                        if (retryCount >= MAX_RETRY_COUNT) {
                            _log.error("Node " + nodeId + " has exceeded max retry count (" + MAX_RETRY_COUNT + "), skipping");
                            continue;
                        }

                        // ????????δ???????????????????????????β
                        retryCountMap.put(nodeId, retryCount + 1);
                        list.add(ganttBean);
                        _log.debug("Parent nodes not ready for node: " + nodeId + ", re-queuing (retry " + (retryCount + 1) + "/" + MAX_RETRY_COUNT + ")");
                        continue;
                    }

                    // ??????????????????????????????
                    boolean parentTimesValid = true;
                    long maxParentEndTime = 0l;
                    for (Object parentIdObj : listParent) {
                        String parentId = (String) parentIdObj;
                        GanttBean parentBean = (GanttBean) mapAct.get(parentId);
                        if (parentBean != null && parentBean.getExpEndTime() != null && !"".equals(parentBean.getExpEndTime())) {
                            try {
                                long parentEndTime = Long.parseLong(parentBean.getExpEndTime());
                                if (parentEndTime > maxParentEndTime) {
                                    maxParentEndTime = parentEndTime;
                                }
                            } catch (NumberFormatException e) {
                                parentTimesValid = false;
                                _log.warn("Invalid parent expEndTime: " + parentBean.getExpEndTime() + " for parent: " + parentId);
                                break;
                            }
                        } else {
                            parentTimesValid = false;
                            _log.warn("Parent node has no valid expEndTime: " + parentId);
                            break;
                        }
                    }

                    if (!parentTimesValid) {
                        // 检查重试次数，防止无限循环
                        String nodeId = ganttBean.getId();
                        int retryCount = retryCountMap.getOrDefault(nodeId, 0);
                        if (retryCount >= MAX_RETRY_COUNT) {
                            _log.error("Node " + nodeId + " has exceeded max retry count (" + MAX_RETRY_COUNT + ") for invalid parent times, skipping");
                            continue;
                        }

                        // 父节点时间无效，重新排队
                        retryCountMap.put(nodeId, retryCount + 1);
                        list.add(ganttBean);
                        _log.debug("Parent nodes have invalid times for node: " + nodeId + ", re-queuing (retry " + (retryCount + 1) + "/" + MAX_RETRY_COUNT + ")");
                        continue;
                    }

                    // ?????????
                    getExpTime(ganttBean, ganttValTimeBean, ganttDelayerBean, dataDate,
                        Integer.valueOf(
                            ganttBean.getAcrossdayNum() == null || "".equals(ganttBean.getAcrossdayNum()) ? "0"
                                    : ganttBean.getAcrossdayNum()),
                        realRunTime, mapAct, atm, listParent, nowTime, avgTimeList);

                    // ????mapAct????????????????????????????????
                    GanttBean ganttBeanOld = (GanttBean) mapAct.get(ganttBean.getId());
                    if (ganttBeanOld == null)
                    {
                        // ???????????????????????????????????????????
                        validateAndFixStartTime(ganttBean, listParent, mapAct);

                        mapAct.put(ganttBean.getId(), ganttBean);
                        if (dependencyMap.containsKey(ganttBean.getId())) {
                            List<String> dependentNodes = dependencyMap.get(ganttBean.getId());
                            for (String depId : dependentNodes) {
                                GanttBean depNode = (GanttBean) mapAct.remove(depId); // ??????
                                if (depNode != null) {
                                    list.add(depNode); // ??????????????????
                                }
                            }
                        }
                        if ("1".equals(ganttBean.getIsProOut()))
                        {
                            break;
                        }
                    } else
                    {
                        ganttBean.setIsQuerySon(ganttBeanOld.getIsQuerySon());
                        // ??????????????
                        if ("1".equals(ganttBeanOld.getIsProOut()) && !"1".equals(ganttBean.getIsProOut()))
                        {
                            if (ganttBean.getActState() == null || "1".equals(ganttBean.getActState())
                                    || "0".equals(ganttBean.getActState()))
                            {
                                // ?????????????????
                                long maxParentEndTime = calculateMaxParentEndTime(listParent, mapAct);

                                // ???????????????????????????
                                long currentStartTime = ganttBean.getExpStartTime() == null
                                    || "".equals(ganttBean.getExpStartTime()) ? 0l
                                    : Long.valueOf(ganttBean.getExpStartTime());
                                long adjustedStartTime = Math.max(currentStartTime, maxParentEndTime);
                                ganttBean.setExpStartTime(String.valueOf(adjustedStartTime));

                                // ????????????????????????????????
                                if (adjustedStartTime > currentStartTime) {
                                    long duration = ganttBean.getExpEndTime() == null
                                        || "".equals(ganttBean.getExpEndTime()) ? 0l
                                        : Long.valueOf(ganttBean.getExpEndTime()) - currentStartTime;
                                    if (duration > 0) {
                                        ganttBean.setExpEndTime(String.valueOf(adjustedStartTime + duration));
                                    }
                                }

                                boolean needUpdate = false;

                                long oldEndTime = ganttBeanOld.getExpEndTime() == null
                                        || "".equals(ganttBeanOld.getExpEndTime()) ? 0l
                                                : Long.valueOf(ganttBeanOld.getExpEndTime());
                                long newEndTime = ganttBean.getExpEndTime() == null
                                        || "".equals(ganttBean.getExpEndTime()) ? 0l
                                                : Long.valueOf(ganttBean.getExpEndTime());
                                if (newEndTime > oldEndTime)
                                {
                                    mapAct.put(ganttBean.getId(), ganttBean);
                                    needUpdate = true;
                                } else
                                {
                                    ganttBean.setExpEndTime(String.valueOf(oldEndTime));
                                    ganttBean.setSourceId(ganttBeanOld.getSourceId());
                                    mapAct.put(ganttBean.getId(), ganttBean);
                                }

                                // ???????????????????????????????
                                if (needUpdate && dependencyMap.containsKey(ganttBean.getId())) {
                                    triggerDependentNodesRecalculation(ganttBean.getId(), dependencyMap, mapAct, list);
                                }
                            }
                        } else if (!"1".equals(ganttBeanOld.getIsProOut()) && "1".equals(ganttBean.getIsProOut()))
                        {
                            if (ganttBean.getActState() == null || "1".equals(ganttBean.getActState())
                                    || "0".equals(ganttBean.getActState()))
                            {
                                // ?????????????????
                                long maxParentEndTime = calculateMaxParentEndTime(listParent, mapAct);

                                // ???????????????????????????
                                long currentStartTime = ganttBean.getExpStartTime() == null
                                    || "".equals(ganttBean.getExpStartTime()) ? 0l
                                    : Long.valueOf(ganttBean.getExpStartTime());
                                long adjustedStartTime = Math.max(currentStartTime, maxParentEndTime);
                                ganttBean.setExpStartTime(String.valueOf(adjustedStartTime));


                                long oldEndTime = ganttBeanOld.getExpEndTime() == null
                                        || "".equals(ganttBeanOld.getExpEndTime()) ? 0l
                                                : Long.valueOf(ganttBeanOld.getExpEndTime());
                                long newEndTime = ganttBean.getExpEndTime() == null
                                        || "".equals(ganttBean.getExpEndTime()) ? 0l
                                                : Long.valueOf(ganttBean.getExpEndTime());
                                if (newEndTime > oldEndTime)
                                {
                                    ganttBeanOld.setExpStartTime(String.valueOf(adjustedStartTime));
                                    ganttBeanOld.setExpEndTime(String.valueOf(newEndTime));
                                    ganttBeanOld.setSourceId(ganttBean.getSourceId());
                                    ganttBeanOld.setUpAllFinish(ganttBean.getUpAllFinish());
                                }else{
                                    ganttBeanOld.setExpStartTime(String.valueOf(adjustedStartTime));
                                }
                                mapAct.put(ganttBean.getId(), ganttBeanOld);
                            }
                        } else
                        {
                            if (ganttBean.getActState() == null || "1".equals(ganttBean.getActState())
                                    || "0".equals(ganttBean.getActState()))
                            {

                                // ?????????????????
                                long maxParentEndTime = calculateMaxParentEndTime(listParent, mapAct);

                                // ???????????????????????????
                                long currentStartTime = ganttBean.getExpStartTime() == null
                                    || "".equals(ganttBean.getExpStartTime()) ? 0l
                                    : Long.valueOf(ganttBean.getExpStartTime());
                                long adjustedStartTime = Math.max(currentStartTime, maxParentEndTime);
                                ganttBean.setExpStartTime(String.valueOf(adjustedStartTime));

                                // ????????????????????????????????
                                if (adjustedStartTime > currentStartTime) {
                                    long duration = ganttBean.getExpEndTime() == null
                                        || "".equals(ganttBean.getExpEndTime()) ? 0l
                                        : Long.valueOf(ganttBean.getExpEndTime()) - currentStartTime;
                                    if (duration > 0) {
                                        ganttBean.setExpEndTime(String.valueOf(adjustedStartTime + duration));
                                    }
                                }


                                long oldEndTime = ganttBeanOld.getExpEndTime() == null
                                        || "".equals(ganttBeanOld.getExpEndTime()) ? 0l
                                                : Long.valueOf(ganttBeanOld.getExpEndTime());
                                long newEndTime = ganttBean.getExpEndTime() == null
                                        || "".equals(ganttBean.getExpEndTime()) ? 0l
                                                : Long.valueOf(ganttBean.getExpEndTime());
                                boolean timeChanged = false;
                                if (newEndTime > oldEndTime )
                                {
                                    mapAct.put(ganttBean.getId(), ganttBean);
                                    timeChanged = true;
                                } else
                                {
                                    ganttBeanOld.setExpStartTime(String.valueOf(adjustedStartTime));
                                    ganttBeanOld.setUpAllFinish(ganttBean.getUpAllFinish());
                                    ganttBean = ganttBeanOld;
                                }

                                // ???????????????????????????????
                                if (timeChanged && dependencyMap.containsKey(ganttBean.getId())) {
                                    triggerDependentNodesRecalculation(ganttBean.getId(), dependencyMap, mapAct, list);
                                }
                            } else
                            {
                                ganttBeanOld.setRunState(ganttBean.getRunState());
                            }
                        }
                    }

                    // ?????????????????????????????????????????????????????????????????
                    if (isExsitAct(listParent, mapAct) && !"1".equals(ganttBean.getIsQuerySon()))
                    {
//                        // ????????
//                        SequenceManager.getInstance().getSonAct(ganttBean, dataDate, listSon, con);
//                        SequenceManager.getInstance().getProOutSonAct(ganttBean, dataDate, listSon, con);
//                        ganttBean.setIsQuerySon("1");

                        // ???????? + ??????????
                        SequenceManager.getInstance().getSonAct(ganttBean, dataDate,listSon, con);
                        SequenceManager.getInstance().getProOutSonAct(ganttBean, dataDate, listSon, con);
                        for (GanttBean son : listSon) {
                            String parentId = ganttBean.getId();
                            String sonId = son.getId();
                            dependencyMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(sonId);
                        }
                        ganttBean.setIsQuerySon("1");
                    }
                }
                for (int ii = 0; ii < listSon.size(); ii++)
                {
                    GanttBean ganttBeanSon = (GanttBean) listSon.get(ii);
                    GanttBean ganttBeanSonOld = (GanttBean) mapAct.get(ganttBeanSon.getId());
                    if (ganttBeanSonOld == null || !"true".equals(ganttBeanSonOld.getUpAllFinish())
                            || "1".equals(ganttBeanSon.getIsProOut()))
                    {
                        list.add(ganttBeanSon);
                    }
                }
                listSon.clear();
                if (list.isEmpty())
                {
                    break;
                }
            }

            Map tmpMap = new HashMap();
            tmpMap.put("mapAct", mapAct);
            tmpMap.put("dataDate", dataDate);
            actList.add(tmpMap);
            Map tmpMap1 = new HashMap();
            tmpMap1.put("realRunTime", realRunTime);
            tmpMap1.put("nowTime", nowTime);
            tmpMap1.put("realTime", realTime);
            tmpMap1.put("mapAct", mapAct);
            tmpMap1.put("dataDate", dataDate);
            actWarnList.add(tmpMap1);
            long a2 = System.currentTimeMillis();
            _log.info("????????:" + dataDate + "  ????=" + (a2 - a1));
        } catch (Exception e)
        {
            _log.error("????????:" + dataDate,e);
        }
    }

    private long getGantFlagByDatadate ()
    {
        String changeTimeStr = AvgTimeManager.getInstance().isConfig(ServerEnv.TIME_AUTO_DAYCUT,
            ServerEnv.TIME_AUTO_DAYCUT_DEFAULT);

        Calendar now = Calendar.getInstance();
        int currentH = now.get(Calendar.HOUR_OF_DAY);
        int currentM = now.get(Calendar.MINUTE);

        int changeH = 0;
        int changeM = 0;
        if (changeTimeStr.contains(":"))
        {
            changeH = Integer.parseInt(changeTimeStr.split(":")[0]);
            changeM = Integer.parseInt(changeTimeStr.split(":")[1]);
        } else
        {
            changeH = Integer.parseInt(changeTimeStr.split(":")[0]);
        }
        // ?????????<???????updateGantData?????????????????????1l,?????????>=???????????0l
        if (currentH < changeH)
        {
            return 1l;
        } else if (currentH == changeH && currentM < changeM)
        {
            return 1l;
        } else
        {
            return 0l;
        }
    }

    /**
     * 
     * @ClassName:  AutoAnalyExcelThread   
     * @Description:????ods???????
     * @author: Administrator 
     * @date:   2019??8??2?? ????9:15:34   
     *     
     * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
     *
     */
    private class AutoAnalyExcelThread extends Thread
    {
        private List<Map<String, String>> list = null;

        public AutoAnalyExcelThread(List<Map<String, String>> list)
        {
            super("AutoAnalyExcelThread");
            this.list = list;
        }

        @Override
        public void run ()
        {
            Connection con = null;
            try
            {
                con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                while (!list.isEmpty())
                {
                    Map<String, Map<String, Map<String, String>>> data = new HashMap<String, Map<String, Map<String, String>>>();
                    PreparedStatement ps = null;
                    PreparedStatement ps1 = null;
                    ResultSet rs = null;
                    String queryDate = "";
                    try
                    {
                        if (list.isEmpty())
                        {
                            break;
                        }
                        Map<String, String> bean = list.remove(0);
                        // String mianPrj = bean.get("IMAINPRONAME")
                        // String sysName = bean.get("ISYSTEM")
                        queryDate = bean.get("queryDate");
                        String subSysName = bean.get("ISUBSYSNAME");
                        String uuid = bean.get("IUUID");
                        long count = Long.parseLong(String.valueOf(bean.get("COUNT")));
                        long analyid = Long.parseLong(String.valueOf(bean.get("ANALYID")));
                        Map<String, Map<String, String>> actMap = new HashMap<String, Map<String, String>>();
                        Map<String, String> dataMap = null;
                        String sql = " select t.*  from ieai_excelmodel t where t.ichildproname=? order by t.ioperationid ";
                        if (count > 0)
                        {
                            // ?????????,?????????
                            sql = " select distinct a.*,(case when b.iid>0 then b.iid  else 0 end) MESSID from ieai_excelmodel a left join ieai_act_relation_mess b on a.ichildproname=b.iprojectname and a.iactname=b.iactname where A.ICHILDPRONAME=?  ORDER BY A.ioperationid ";
                            ps = con.prepareStatement(sql);
                            ps.setString(1, subSysName);
                            rs = ps.executeQuery();
                            while (rs.next())
                            {
                                dataMap = new HashMap<String, String>();
                                dataMap.put("DATASTATE", rs.getString("MESSID"));
                                dataMap.put("IOPERATIONID", rs.getString("IOPERATIONID"));
                                dataMap.put("IAGENTSOURCEGROUP", rs.getString("IAGENTSOURCEGROUP"));
                                dataMap.put("ICHILDPRONAME", rs.getString("ICHILDPRONAME"));
                                dataMap.put("IACTNAME", rs.getString("IACTNAME"));
                                dataMap.put("IACTDESCRIPTION", rs.getString("IACTDESCRIPTION"));
                                dataMap.put("ISHELLHOUSE", rs.getString("ISHELLHOUSE"));
                                dataMap.put("ISHELLABSOLUTEPATH", rs.getString("ISHELLABSOLUTEPATH"));
                                dataMap.put("UUID", uuid);
                                actMap.put(rs.getString("IOPERATIONID"), dataMap);
                            }
                            String s = " update  IEAI_TOPO_ANALY_MODEL  set ISUBSYSNAME=? ,IANALYTIME=FUN_GET_DATE_NUMBER_NEW("
                                    + Constants.getCurrentSysDate() + ",8)  WHERE IID=? ";
                            ps1 = con.prepareStatement(s);
                            ps1.setString(1, subSysName);
                            ps1.setLong(2, analyid);
                            ps1.executeUpdate();
                        } else
                        {
                            // ?????????????
                            sql = " select t.* from ieai_excelmodel t where t.ichildproname=? order by t.ioperationid ";
                            ps = con.prepareStatement(sql);
                            ps.setString(1, subSysName);
                            rs = ps.executeQuery();
                            while (rs.next())
                            {
                                dataMap = new HashMap<String, String>();
                                dataMap.put("DATASTATE", "0");
                                dataMap.put("IOPERATIONID", rs.getString("IOPERATIONID"));
                                dataMap.put("IAGENTSOURCEGROUP", rs.getString("IAGENTSOURCEGROUP"));
                                dataMap.put("ICHILDPRONAME", rs.getString("ICHILDPRONAME"));
                                dataMap.put("IACTNAME", rs.getString("IACTNAME"));
                                dataMap.put("IACTDESCRIPTION", rs.getString("IACTDESCRIPTION"));
                                dataMap.put("ISHELLHOUSE", rs.getString("ISHELLHOUSE"));
                                dataMap.put("ISHELLABSOLUTEPATH", rs.getString("ISHELLABSOLUTEPATH"));
                                dataMap.put("UUID", uuid);
                                actMap.put(rs.getString("IOPERATIONID"), dataMap);
                            }
                            String s = " insert into IEAI_TOPO_ANALY_MODEL(iid,ISUBSYSNAME,IANALYTIME) values(?,?,FUN_GET_DATE_NUMBER_NEW("
                                    + Constants.getCurrentSysDate() + ",8))";
                            long id = IdGenerator.createId("IEAI_TOPO_ANALY_MODEL", con);
                            ps1 = con.prepareStatement(s);
                            ps1.setLong(1, id);
                            ps1.setString(2, subSysName);
                            ps1.executeUpdate();
                        }
                        data.put(subSysName, actMap);
                        con.commit();
                    } catch (Exception ex)
                    {
                        _log.error("Error when AutoAnalyExcelThread and mes:", ex);
                    } finally
                    {
                        DBResource.closePreparedStatement(ps1, "AutoAnalyExcelThread", _log);
                        DBResource.closePSRS(rs, ps, "AutoAnalyExcelThread", _log);
                    }
                    MainFlowCfg.getInstance().orgActInsertIntoMess(data, queryDate);
//                    this.orgActInsertIntoMess(data, queryDate);
                }
            } catch (Exception tt)
            {
                try
                {
                    con.rollback();
                } catch (SQLException e)
                {
                    _log.error(e);
                }
                _log.error("AutoAnalyExcelThread error and next! ErrorInfo:", tt);
            } finally
            {
                DBResource.closeConnection(con, "AutoAnalyExcelThread", _log);
            }
        }

        /**
         * 
         * @Title: orgActInsertIntoMess   
         * @Description: ????????????????mess??
         * @param data      
         * @author: Administrator 
         * @date:   2019??8??2?? ????9:16:03
         */
        public void orgActInsertIntoMess ( Map<String, Map<String, Map<String, String>>> data, String queryDate )
        {
            String method = Thread.currentThread().getStackTrace()[1].getMethodName();
            String insert = " insert into ieai_act_relation_mess(IID,IPROJECTNAME,IACTNAME,IACTDES,IACTSTATE,IAGENTIP,IFLOWSTATE,ibatchname,ibatchpath,IFLOWNAME,IPROJECTID,iparallelflag) values(?,?,?,?,1,?,0,?,?,?,?,?)";
            String update = " update ieai_act_relation_mess set IACTDES=?,IAGENTIP=?,ibatchname=?,ibatchpath=?,IPROJECTID=? where IID=?";
            Map<String, String> relation = new HashMap<String, String>();
            Connection con = null;
            try
            {
                con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                for (Map.Entry<String, Map<String, Map<String, String>>> entry : data.entrySet())
                {
                    PreparedStatement ps1 = null;
                    try
                    {
                        String prjKey = entry.getKey();
                        Map<String, Map<String, String>> prjValue = entry.getValue();

                        String sql = " delete from ieai_act_relation_mess where iid not in (select m.iid from ieai_act_relation_mess m,ieai_excelmodel n where m.iprojectname=n.ichildproname and m.iactname=n.iactname and n.ichildproname=?) AND iprojectname=?";

                        if (JudgeDB.IEAI_DB_TYPE == 3)
                        {
                            sql = "delete from ieai_act_relation_mess where iid NOT in ( SELECT E.IID  FROM (select m.iid from ieai_act_relation_mess m,ieai_excelmodel n where m.iprojectname=n.ichildproname and m.iactname=n.iactname and n.ichildproname=?) AS E) AND iprojectname=?";
                        }
                        ps1 = con.prepareStatement(sql);
                        ps1.setString(1, prjKey);
                        ps1.setString(2, prjKey);
                        ps1.executeUpdate();
                        for (Map.Entry<String, Map<String, String>> entry1 : prjValue.entrySet())
                        {
                            PreparedStatement ps = null;
                            try
                            {
                                Map<String, String> idValue = entry1.getValue();
                                long saveOrUpdate = Long.parseLong(idValue.get("DATASTATE"));
                                String operationId = idValue.get("IOPERATIONID");
                                String agent = idValue.get("IAGENTSOURCEGROUP");
                                String prjName = idValue.get("ICHILDPRONAME");
                                String actName = idValue.get("IACTNAME");
                                String actDes = idValue.get("IACTDESCRIPTION");
                                String startShell = idValue.get("ISHELLHOUSE");
                                String shell = idValue.get("ISHELLABSOLUTEPATH");
                                String newId = idValue.get("DATASTATE");
                                String uuid = idValue.get("UUID");
                                if (saveOrUpdate == 0)
                                {
                                    // insert
                                    long mesId = IdGenerator.createId("ieai_act_relation_mess", con);
                                    newId = String.valueOf(mesId);
                                    ps = con.prepareStatement(insert);
                                    ps.setLong(1, mesId);
                                    ps.setString(2, prjName);
                                    ps.setString(3, actName);
                                    ps.setString(4, actDes);
                                    ps.setString(5, agent);
                                    ps.setString(6, startShell);
                                    ps.setString(7, shell);
                                    ps.setString(8, actName);
                                    ps.setString(9, uuid);
                                    ps.setInt(10, 1);
                                    ps.executeUpdate();
                                } else
                                {
                                    // update
                                    ps = con.prepareStatement(update);
                                    ps.setString(1, actDes);
                                    ps.setString(2, agent);
                                    ps.setString(3, startShell);
                                    ps.setString(4, shell);
                                    ps.setString(5, uuid);
                                    ps.setLong(6, saveOrUpdate);
                                    ps.executeUpdate();
                                }
                                relation.put(operationId, newId);
                            } catch (Exception e)
                            {
                                _log.error(method, e);
                            } finally
                            {
                                DBResource.closePreparedStatement(ps, method, _log);
                            }
                        }
                        this.orgActInsertIntoRelation(con, relation, prjKey, queryDate);
                    } catch (Exception e)
                    {
                        _log.error(method, e);
                    } finally
                    {
                        DBResource.closePreparedStatement(ps1, method, _log);
                    }
                }
                con.commit();
            } catch (Exception tt)
            {
                _log.error(method + " error and next! ErrorInfo:", tt);
            } finally
            {
                DBResource.closeConnection(con, method, _log);
            }
        }

        /**
         * 
         * @Title: orgActInsertIntoMess   
         * @Description: ???????????????????relation??
         * @param data      
         * @author: Administrator 
         * @date:   2019??8??2?? ????9:16:03
         */
        public void orgActInsertIntoRelation ( Connection con, Map<String, String> relation, String prjName,
                String queryDate )
        {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Map<String, String> newRelation = null;
            List<Map<String, String>> listt = new ArrayList<Map<String, String>>();
            String method = Thread.currentThread().getStackTrace()[1].getMethodName();
            PreparedStatement ps = null;
            PreparedStatement ps1 = null;
            PreparedStatement ps0 = null;
            PreparedStatement ps00 = null;
            PreparedStatement ps4 = null;
            PreparedStatement ps5 = null;
            ResultSet rs = null;
            try
            {
                String sql = "select a.ioperationid,a.iselfoperationid  from ieai_excelmodel t,ieai_actsucc a where t.ioperationid=a.ioperationid and t.ICHILDPRONAME=?";
                ps = con.prepareStatement(sql);
                ps.setString(1, prjName);
                rs = ps.executeQuery();
                while (rs.next())
                {
                    PreparedStatement ps2 = null;
                    ResultSet rs2 = null;
                    String source = rs.getString(1);
                    String target = rs.getString(2);
                    String newsource = relation.get(source);
                    String newtarget = relation.get(target);
                    try
                    {
                        newRelation = new HashMap<String, String>();
                        newRelation.put(newsource, newtarget);
                        long count = 0;
                        if (null != newtarget && !"".equals(newtarget))
                        {
                            String sqls = "SELECT COUNT(1) FROM ieai_act_relation WHERE iactmesid=? AND ISONACTMESID=?";
                            ps2 = con.prepareStatement(sqls);
                            ps2.setLong(1, Long.parseLong(newsource));
                            ps2.setLong(2, Long.parseLong(newtarget));
                            rs2 = ps2.executeQuery();
                            while (rs2.next())
                            {
                                count = rs2.getLong(1);
                            }
                        }
                        if (count == 0)
                        {
                            listt.add(newRelation);
                        }
                    } catch (Exception e)
                    {
                        _log.error(method + "s:" + newsource + ",t:" + newtarget, e);
                    } finally
                    {
                        DBResource.closePSRS(rs2, ps2, method, _log);
                    }
                }
                // if (!list.isEmpty())
                // {
                // String sqldelete = " delete from ieai_act_relation where iactmesid in (select
                // b.iid from ieai_act_relation_mess b where b.iprojectname=?)";
                // ps2 = con.prepareStatement(sqldelete);
                // ps2.setString(1, prjName);
                // ps2.executeUpdate();
                // }
                String sqlinsert = " insert into ieai_act_relation(iid,IACTMESID,ISONACTMESID) values(?,?,?)";
                ps1 = con.prepareStatement(sqlinsert);
                for (Map<String, String> map : listt)
                {
                    long iid = IdGenerator.createId("ieai_act_relation", con);
                    ps1.setLong(1, iid);
                    String key = "";
                    String value = "";
                    for (Map.Entry<String, String> entry : map.entrySet())
                    {
                        key = entry.getKey();
                        value = entry.getValue();
                    }
                    ps1.setString(2, key);
                    ps1.setString(3, value);
                    ps1.addBatch();
                }
                ps1.executeBatch();

                String sqldel = " DELETE FROM IEAI_ACT_TOPO_INSTANCE  WHERE IID NOT IN (SELECT A.IID FROM IEAI_ACT_RELATION_MESS A WHERE A.IPROJECTNAME=?) AND IPROJECTNAME=? AND IDATADATE = ?";
                ps0 = con.prepareStatement(sqldel);
                ps0.setString(1, prjName);
                ps0.setString(2, prjName);
                ps0.setString(3, queryDate);
                ps0.executeUpdate();

                String sqldel4 = " DELETE FROM IEAI_EXEACT_TOPO_INSTANCE  WHERE IID NOT IN (SELECT A.IID FROM IEAI_ACT_RELATION_MESS A WHERE A.IPROJECTNAME=?) AND IPROJECTNAME=? AND IDATADATE = ?";
                ps4 = con.prepareStatement(sqldel4);
                ps4.setString(1, prjName);
                ps4.setString(2, prjName);
                ps4.setString(3, queryDate);
                ps4.executeUpdate();

                String dataDate = sdf.format(getDateBefore(sdf.parse(queryDate), 1));
                sqldel = " DELETE FROM IEAI_ACT_TOPO_INSTANCE  WHERE iactstate=1 and IID NOT IN (SELECT A.IID FROM IEAI_ACT_RELATION_MESS A WHERE A.IPROJECTNAME=?) AND IPROJECTNAME=? AND IDATADATE = ?";
                ps00 = con.prepareStatement(sqldel);
                ps00.setString(1, prjName);
                ps00.setString(2, prjName);
                ps00.setString(3, dataDate);
                ps00.executeUpdate();

                String sqldel5 =  "DELETE FROM IEAI_EXEACT_TOPO_INSTANCE WHERE IID IN(select IID FROM IEAI_ACT_TOPO_INSTANCE  WHERE iactstate=1 and IID NOT IN (SELECT A.IID FROM IEAI_ACT_RELATION_MESS A WHERE A.IPROJECTNAME=?) AND IPROJECTNAME=? AND IDATADATE = ?)";
                ps5 = con.prepareStatement(sqldel5);
                ps5.setString(1, prjName);
                ps5.setString(2, prjName);
                ps5.setString(3, dataDate);
                ps5.executeUpdate();
            } catch (Exception tt)
            {
                _log.error(method + " error and next! Err:", tt);
            } finally
            {
                DBResource.closePreparedStatement(ps1, method, _log);
                DBResource.closePreparedStatement(ps0, method, _log);
                DBResource.closePreparedStatement(ps4, method, _log);
                DBResource.closePreparedStatement(ps00, method, _log);
                DBResource.closePreparedStatement(ps5, method, _log);
                DBResource.closePSRS(rs, ps, method, _log);
            }
        }

    }

    /**
     * 
     * @Title: execActTimeCalculateAnaly   
     * @Description: ???????ods?????topo??????????jx????????
     * @param flag      
     * @author: Administrator 
     * @date:   2019??8??2?? ????9:10:58
     */
    public void execActTimeCalculateAnaly ( String queryDate )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection con = null;
        ConfigReader cr = ConfigReader.getInstance();
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        try
        {
            cr.init();
            boolean analyExcelSwitch = cr.getBooleanProperties(ServerEnv.TOPO_ANALY_EXCEL_SWITCH,
                ServerEnv.FALSE_BOOLEAN);
            String specialSys = cr.getProperties(ServerEnv.TOPO_ANALY_EXCEL_SPECIAL_SYS, "");
            List<Map<String, String>> list = null;
            if (analyExcelSwitch)
            {
                long startTime = System.currentTimeMillis();
                _log.info("ActTimeCalculateManager.execActTimeCalculateAnaly begin analy ods-excel!");
                String sqlWhere = "";
                if (!"".equals(specialSys))
                {
                    specialSys = StringUtils.replace(specialSys, ",", "','");
                    sqlWhere = " and t.ICHILDPRONAME not in ('" + specialSys + "')";
                }
                String dateTime = Environment.getTopoAnalyzeTime();
//                _log.info("topo.analyze.time:"+dateTime);
                boolean flag;
                try
                {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    sdf.parse(dateTime);
                    flag = true;
                } catch (ParseException e)
                {
                    flag = false;
                }
                String sqlWhere1="";
                if(flag){
                    sqlWhere1 = " and t.IUPLOADTIME >= '"+dateTime+"' ";
                }
                list = new ArrayList<Map<String, String>>();
                String sql = " SELECT DISTINCT E.IMAINPRONAME,E.ISYSTEM,E.ICHILDPRONAME,(CASE WHEN M.IID IS NULL THEN 0 ELSE M.IID END) ANALYID,(CASE WHEN M.ISUBSYSNAME IS NULL THEN 0 ELSE 1 END) TIMES,IUUID  FROM (SELECT T.IMAINPRONAME,T.ISYSTEM,T.ICHILDPRONAME,iuuid FROM (select e.*,t.iuuid from ieai_excelmodel e,IEAI_PROJECT t where e.ichildproname=t.iname and t.iid=t.ilatestid "+sqlWhere1+") T where 1=1 "
                        + sqlWhere + ") E LEFT JOIN IEAI_TOPO_ANALY_MODEL M  ON E.ICHILDPRONAME=M.ISUBSYSNAME ";
                con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                ps = con.prepareStatement(sql);
                rs = ps.executeQuery();

                Map<String, String> map = null;
                while (rs.next())
                {
                    map = new HashMap<String, String>();
                    map.put("IMAINPRONAME", rs.getString("IMAINPRONAME"));
                    map.put("ISYSTEM", rs.getString("ISYSTEM"));
                    map.put("ISUBSYSNAME", rs.getString("ICHILDPRONAME"));
                    map.put("COUNT", rs.getString("TIMES"));
                    map.put("ANALYID", rs.getString("ANALYID"));
                    map.put("IUUID", rs.getString("IUUID"));
                    map.put("queryDate", queryDate);
                    list.add(map);
                }
                _log.info("ActTimeCalculateManager.execActTimeCalculateAnaly execute ods-excel list size is:"+list.size());
                AutoAnalyExcelThread autoAnalyExcelThread = new AutoAnalyExcelThread(list);
                autoAnalyExcelThread.start();
                _log.info("ActTimeCalculateManager.execActTimeCalculateAnaly end analy ods-excel!---"+(System.currentTimeMillis()-startTime));
            }
        } catch (Exception e)
        {
            _log.error(method, e);
        } finally
        {
            DBResource.closePSRS(rs1, ps1, method, _log);
            DBResource.closeConn(con, rs, ps, method, _log);
        }
    }

    /**
     * ???????????????????
     */
    public synchronized void execActTimeCalculate ( boolean flag, String dataDates )
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String queryDate = sdf.format(new Date());
        Connection con = null;
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            if (flag)
            {
                while (true)
                {
                    //????????????state:0 ????,1 ???????,2 studio???
                    boolean calculateing = getOperactionState(0, con);
                    if (!calculateing)
                    {
                        _log.info("flow analy running,please wait??");
                        Thread.sleep(2000);
                    } else
                    {
                        setOperactionState(1);
                        break;
                    }
                }
            }
            // ??????????????ods???????
            long nowTime = System.currentTimeMillis();
            long a = System.currentTimeMillis();
            List actList = new ArrayList();
            List actWarnList = new ArrayList();
            if (flag)
            {
                //?jx??????????????????begin
//                execFrequency++;
//                if(execFrequency==1) {
//                this.execActTimeCalculateAnaly(queryDate);
//                }else {
//                    execFrequency--;
//                }
                //???????ods?????topo???????
                this.execActTimeCalculateAnaly(queryDate);
                //?jx??????????????????end
                String showLevel = ServerEnv.getInstance().getSysConfig("analyModel",
                    String.valueOf(ServerEnv.TOPO_ANALYMODEL_DEFAULT));
                if ("2".equals(showLevel) || "3".equals(showLevel))
                {
                    String datadate1 = GetDataDateManager.getInstance().getDataDate(0);
                    boolean dateflag = true;
                    if(queryDate.equals(datadate1)) {
                        //??????????
                        dateflag = false;
                    }
                    for (int i = 0; i < 2; i++)
                    {
                        if(dateflag || (!dateflag&&i == 0)) {
                            String dataDate = sdf.format(getDateBefore(sdf.parse(queryDate), i));
                            operation(con, actList, actWarnList, dataDate, i, flag, a);
                        }
                    }
                    /*if (null != actList && actList.size() == 2)
                    {
                        String dataDate = "";
                        long a2 = System.currentTimeMillis();
                        SequenceManager.getInstance().updateActExp((String) ((Map) actList.get(0)).get("dataDate"),
                            (Map) ((Map) actList.get(0)).get("mapAct"), (String) ((Map) actList.get(1)).get("dataDate"),
                            (Map) ((Map) actList.get(1)).get("mapAct"), con);
                        long a3 = System.currentTimeMillis();
                        _log.info("????????:" + dataDate + "updateActExp update=" + (a3 - a2));
                    }*/
                    if (null != actList &&  actList.size()>0)
                    {
                        String dataDate = "";
                        long a2 = System.currentTimeMillis();
                        if(actList.size()>1){
                            SequenceManager.getInstance().updateActExp((String) ((Map) actList.get(0)).get("dataDate"),
                                    (Map) ((Map) actList.get(0)).get("mapAct"), (String) ((Map) actList.get(1)).get("dataDate"),
                                    (Map) ((Map) actList.get(1)).get("mapAct"), con);
                        }else{
                            SequenceManager.getInstance().updateActExp((String) ((Map) actList.get(0)).get("dataDate"),
                                    (Map) ((Map) actList.get(0)).get("mapAct"), null,
                                    null, con);
                        }

                        long a3 = System.currentTimeMillis();
                        _log.info("????????:" + dataDate + "updateActExp update=" + (a3 - a2));
                    }
                } else
                {
                    ConfigReader cr = ConfigReader.getInstance();
                    boolean timeSwitch = false;
                    String autodaycut = "22";
                    try
                    {
                        cr.init();
                        timeSwitch = Boolean.parseBoolean(
                            cr.getProperties(ServerEnv.TIME_SWITCH, String.valueOf(ServerEnv.TIME_SWITCH_DEFAULT)));
                        autodaycut = AvgTimeManager.getInstance().isConfig(ServerEnv.TIME_AUTO_DAYCUT,
                            ServerEnv.TIME_AUTO_DAYCUT_DEFAULT);
                    } catch (IOException e)
                    {
                        autodaycut = "22";
                        timeSwitch = ServerEnv.getInstance().getBooleanConfig(ServerEnv.TIME_SWITCH,
                            ServerEnv.TIME_SWITCH_DEFAULT);
                    }
                    String currentTime = queryDate;
                    Date date = new Date();
                    if (timeSwitch)
                    {
                        Calendar cal = Calendar.getInstance();
                        cal.add(Calendar.DATE, -1);
                        String hour = "22";
                        String minute = "00";
                        if (null != autodaycut && !"".equals(autodaycut) && !"null".equals(autodaycut))
                        {

                            if (autodaycut.split(":").length == 1)
                            {
                                hour = autodaycut.split(":")[0];
                            } else
                            {
                                hour = autodaycut.split(":")[0];
                                minute = autodaycut.split(":")[1];
                            }
                            int h = 22;
                            int m = 00;
                            if (null != hour && !"".equals(hour) && !"null".equals(hour))
                            {
                                try
                                {
                                    h = Integer.parseInt(hour);
                                } catch (Exception e)
                                {
                                    h = 22;
                                }
                            }
                            if (null != minute && !"".equals(minute) && !"null".equals(minute))
                            {
                                try
                                {
                                    m = Integer.parseInt(minute);
                                } catch (Exception e)
                                {
                                    m = 00;
                                }
                            }
                            if (date.getHours() > h)
                            {
                                currentTime = sdf.format(date);
                            } else if (date.getHours() == h)
                            {
                                if (date.getMinutes() >= m)
                                {
                                    currentTime = sdf.format(date);
                                } else
                                {
                                    currentTime = sdf.format(cal.getTime());
                                }
                            } else
                            {
                                currentTime = sdf.format(cal.getTime());
                            }
                        } else
                        {
                            currentTime = sdf.format(cal.getTime());
                        }
                    } else
                    {
                        currentTime = sdf.format(date);
                    }
                    operation(con, actList, actWarnList, currentTime, 0, flag, a);
                    if (null != actList && !actList.isEmpty())
                    {
                        long a2 = System.currentTimeMillis();
                        SequenceManager.getInstance().updateActExp((String) ((Map) actList.get(0)).get("dataDate"),
                            (Map) ((Map) actList.get(0)).get("mapAct"), null, null, con);
                        long a3 = System.currentTimeMillis();
                        _log.info("updateActExp update  time=" + (a3 - a2));
                    }
                }
                con.commit();
            } else
            {
                operation(con, actList, actWarnList, dataDates, 0, flag, a);
                if (null != actList && !actList.isEmpty())
                {
                    long a2 = System.currentTimeMillis();
                    SequenceManager.getInstance().updateActExp((String) ((Map) actList.get(0)).get("dataDate"),
                        (Map) ((Map) actList.get(0)).get("mapAct"), null, null, con);
                    long a3 = System.currentTimeMillis();
                    _log.info("updateActExp update  time=" + (a3 - a2));
                }
                con.commit();
            }

            if (null != actWarnList && !actWarnList.isEmpty())
            {
                String dataDate = "";
                Map mapActs = null;
                Map mapAct = null;
                long realRunTime = 0;
                long realTime = 0;
                for (int i = 0; i < actWarnList.size(); i++)
                {
                    mapActs = (Map) actWarnList.get(i);
                    dataDate = (String) mapActs.get("dataDate");
                    mapAct = (Map) mapActs.get("mapAct");
                    realRunTime = (Long) mapActs.get("realRunTime");
                    realTime = (Long) mapActs.get("realTime");
                    long a3 = System.currentTimeMillis();
                    actWarn(dataDate, mapAct, nowTime, con, realRunTime, realTime);
                    long a4 = System.currentTimeMillis();
                    _log.info("????????:" + dataDate + " actWarn update time=" + (a4 - a3));
                    con.commit();
                }
            }
            con.commit();
            long b = System.currentTimeMillis();
            _log.info("ActTimeCalculateThread finish??run time??" + (b - a));
        } catch (Exception e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
                _log.error("ActTimeCalculateThread.run rooback is err", e1);
            }
            _log.error("ActTimeCalculateThread.run", e);
        } finally
        {
            try
            {
                setOperactionState();
            } catch (Exception e2)
            {
                _log.error("ActTimeCalculateThread.run setOperactionState is err", e2);
            }
            DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
    }

    public GanttBean getExpTime ( GanttBean ganttBean, GanttValTimeBean ganttValTimeBean,
            GanttDelayerBean ganttDelayerBean, String dataDate, int iacrossdayNum, long realRunTime, Map mapAct,
            ActTimeModel atm, List listParent, long nowTime, List avgTimeList )
    {
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try
        {
            GanttBean ganttBeanParent = (GanttBean) mapAct.get(ganttBean.getParentId());

            // ???????
            if (ganttBeanParent != null && ganttBeanParent.getSourceId() != null
                    && !"".equals(ganttBeanParent.getSourceId()))
            {
                if (ganttBean.getActState() == null || "1".equals(ganttBean.getActState()))
                {
                    ganttBean.setSourceId(ganttBeanParent.getSourceId());
                } else if ("0".equals(ganttBean.getActState()))
                {
                    ganttBean.setSourceId(ganttBean.getId());
                } else
                {
                    ganttBean.setSourceId(null);
                }
            } else
            {
                if (ganttBean.getActState() == null || "1".equals(ganttBean.getActState())
                        || "0".equals(ganttBean.getActState()))
                {
                    ganttBean.setSourceId(ganttBean.getId());
                } else
                {
                    ganttBean.setSourceId(null);
                }
            }

            // ???????????
            if ("1".equals(ganttBean.getIsProOut()))
            {
                long upEndTime = 0l;
                if (ganttBeanParent != null && (ganttBeanParent.getActState() == null
                        || "1".equals(ganttBeanParent.getActState()) || "0".equals(ganttBeanParent.getActState())))
                {

                    upEndTime = ganttBeanParent.getExpEndTime() == null || "".equals(ganttBeanParent.getExpEndTime())
                            ? 0l
                            : Long.valueOf(ganttBeanParent.getExpEndTime()) + realRunTime;
                } else if (ganttBeanParent != null
                        && ("2".equals(ganttBeanParent.getActState()) || "3".equals(ganttBeanParent.getActState())))
                {
                    upEndTime = ganttBeanParent.getRealEndTime() == null || "".equals(ganttBeanParent.getRealEndTime())
                            ? 0l
                            : Long.valueOf(ganttBeanParent.getRealEndTime()) + realRunTime;

                }
                if (ganttBean.getActState() == null || "1".equals(ganttBean.getActState())
                        || "0".equals(ganttBean.getActState()))
                {
                    ganttBean.setExpStartTime(null);
                    if (atm.getExpEndTime() != null && !"".equals(atm.getExpEndTime()))
                    {
                        ganttBean.setExpEndTime(atm.getExpEndTime());
                    } else
                    {
                        ganttBean.setExpEndTime(String.valueOf(upEndTime));
                    }
                } else
                {
                    ganttBean.setExpStartTime(null);
                    ganttBean.setExpEndTime(null);
                }
            } else
            {
                if ("1".equals(ganttBean.getRunState()))// ?????????????
                {
                    ganttBean.setRunState("1");
                    ganttBean.setExpStartTime(null);
                    ganttBean.setExpEndTime(null);
                } else
                {
                    Date mainFlowStartDate = getDateAfter(sdf1.parse(dataDate), iacrossdayNum);
                    // ?????????
                    // ??????
                    long avgTime = Long
                            .parseLong(ganttBean.getAvgTime() == null || "".equals(ganttBean.getAvgTime()) ? "0"
                                    : ganttBean.getAvgTime());

                    // ??????????
                    if (!validTime(ganttValTimeBean, mainFlowStartDate))
                    {
                        avgTime = 0l;
                        ganttBean.setRunState("2");
                    }

                    if (atm.getHavgTime() != null && !Long.valueOf("0").equals(atm.getHavgTime()))
                    {
                        avgTime = atm.getHavgTime();
                    }

                    //?????????????????? ??????
                    avgTime = realRunTime;

                    // ????????
                    long beginTime = 0l;
                    // ?????????
                    long endTime;
                    // ??????
                    // ??????????????????????????
                    if (ganttBean.getActState() == null || "1".equals(ganttBean.getActState()))
                    {
                        // ????????????????

                        long upEndTime = 0l;

                        if (isExsitAct(listParent, mapAct))
                        {
                            for (int i = 0; i < listParent.size(); i++)
                            {
                                String id = (String) listParent.get(i);
                                GanttBean ganttBeanUpAll = (GanttBean) mapAct.get(id);
                                long upAllTime = 0l;
                                if (ganttBeanUpAll != null
                                        && (ganttBeanUpAll.getActState() == null
                                                || "1".equals(ganttBeanUpAll.getActState())
                                                || "0".equals(ganttBeanUpAll.getActState()))
                                        && ganttBean.getProjectName().equals(ganttBeanUpAll.getProjectName()))
                                {
                                    if ("1".equals(ganttBeanUpAll.getIsSyncCall()))
                                    {
                                        upAllTime = ganttBeanUpAll.getExpStartTime() == null
                                                || "".equals(ganttBeanUpAll.getExpStartTime()) ? 0l
                                                        : Long.valueOf(ganttBeanUpAll.getExpStartTime());
                                    } else
                                    {
                                        upAllTime = ganttBeanUpAll.getExpEndTime() == null
                                                || "".equals(ganttBeanUpAll.getExpEndTime()) ? 0l
                                                        : Long.valueOf(ganttBeanUpAll.getExpEndTime());
                                    }
                                } else if (ganttBeanUpAll != null
                                        && ("2".equals(ganttBeanUpAll.getActState())
                                                || "3".equals(ganttBeanUpAll.getActState()))
                                        && ganttBean.getProjectName().equals(ganttBeanUpAll.getProjectName()))
                                {
                                    if ("1".equals(ganttBeanUpAll.getIsSyncCall()))
                                    {
                                        upAllTime = ganttBeanUpAll.getRealBeginTime() == null
                                                || "".equals(ganttBeanUpAll.getRealBeginTime()) ? 0l
                                                        : Long.valueOf(ganttBeanUpAll.getRealBeginTime());
                                    } else
                                    {
                                        upAllTime = ganttBeanUpAll.getRealEndTime() == null
                                                || "".equals(ganttBeanUpAll.getRealEndTime()) ? 0l
                                                        : Long.valueOf(ganttBeanUpAll.getRealEndTime());
                                    }

                                }
                                upEndTime = upAllTime > upEndTime ? upAllTime : upEndTime;
                            }
                            ganttBean.setUpAllFinish("true");
                        } else
                        {
                            // ????????????
                            if (ganttBeanParent != null && (ganttBeanParent.getActState() == null
                                    || "1".equals(ganttBeanParent.getActState())
                                    || "0".equals(ganttBeanParent.getActState())))
                            {
                                if ("1".equals(ganttBeanParent.getIsSyncCall()))
                                {
                                    upEndTime = ganttBeanParent.getExpStartTime() == null
                                            || "".equals(ganttBeanParent.getExpStartTime()) ? 0l
                                                    : Long.valueOf(ganttBeanParent.getExpStartTime());
                                } else
                                {
                                    upEndTime = ganttBeanParent.getExpEndTime() == null
                                            || "".equals(ganttBeanParent.getExpEndTime()) ? 0l
                                                    : Long.valueOf(ganttBeanParent.getExpEndTime());
                                }
                            } else if (ganttBeanParent != null && ("2".equals(ganttBeanParent.getActState())
                                    || "3".equals(ganttBeanParent.getActState())))
                            {
                                if ("1".equals(ganttBeanParent.getIsSyncCall()))
                                {
                                    upEndTime = ganttBeanParent.getRealBeginTime() == null
                                            || "".equals(ganttBeanParent.getRealBeginTime()) ? 0l
                                                    : Long.valueOf(ganttBeanParent.getRealBeginTime());
                                } else
                                {
                                    upEndTime = ganttBeanParent.getRealEndTime() == null
                                            || "".equals(ganttBeanParent.getRealEndTime()) ? 0l
                                                    : Long.valueOf(ganttBeanParent.getRealEndTime());
                                }

                            }
                        }

                        if (upEndTime != 0l)
                        {
                            ganttBean.setUpActEndTime(String.valueOf(upEndTime));
                        }

                        // ??????????????????????????????????????????????????????????????
                        upEndTime = nowTime > upEndTime ? nowTime : upEndTime;

                        // ???????????
                        if (ganttDelayerBean == null)
                        {
                            if (atm.getExpStartTime() != null && !"".equals(atm.getExpStartTime()))
                            {
                                beginTime = Long.valueOf(atm.getExpStartTime());
                            } else
                            {
                                if (ganttBeanParent != null)
                                {
                                    beginTime = upEndTime;
                                } else
                                {
                                    beginTime = upEndTime + iacrossdayNum * 24 * 60 * 60 * 1000;
                                }
                            }
                            if (atm.getExpEndTime() != null && !"".equals(atm.getExpEndTime()))
                            {
                                endTime = Long.valueOf(atm.getExpEndTime());
                            } else
                            {
                                endTime = beginTime + avgTime;
                            }
                        } else
                        {
                            // ????????????
                            if (Long.valueOf("0").equals(ganttDelayerBean.getDelayertype()))
                            {
                                String delayerTime = "";
                                String dayType = "";
                                String[] timeConfig = (ganttDelayerBean.getTimestr()).split("\\:");
                                delayerTime = ("*".equals(timeConfig[3]) ? "0" : timeConfig[3]) + ":"
                                        + ("*".equals(timeConfig[4]) ? "0" : timeConfig[4]) + ":"
                                        + ("*".equals(timeConfig[5]) ? "0" : timeConfig[5]);
                                if (timeConfig.length == 7)
                                {
                                    dayType = timeConfig[6];
                                }

                                // ???????????

                                if ("".equals(dayType) || "0".equals(dayType))
                                {
                                    if (ganttBean.getUpActEndTime() != null && !"".equals(ganttBean.getUpActEndTime()))
                                    {
                                        beginTime = sdf2.parse(sdf1.format(new Date(upEndTime)) + " " + delayerTime)
                                                .getTime();
                                        if (upEndTime > beginTime)
                                        {
                                            beginTime = beginTime + 24 * 60 * 60 * 1000;
                                        }
                                    } else
                                    {
                                        beginTime = sdf2.parse(sdf1.format(mainFlowStartDate) + " " + delayerTime)
                                                .getTime();
                                        if (upEndTime > beginTime)
                                        {
                                            beginTime = upEndTime;
                                        }
                                    }
                                } else if ("1".equals(dayType))
                                {
                                    beginTime = sdf2.parse(sdf1.format(mainFlowStartDate) + " " + delayerTime)
                                            .getTime();
                                    if (upEndTime > beginTime)
                                    {
                                        beginTime = upEndTime;
                                    }
                                } else if ("2".equals(dayType))
                                {
                                    beginTime = sdf2.parse(sdf1.format(mainFlowStartDate) + " " + delayerTime).getTime()
                                            + 24 * 60 * 60 * 1000;
                                    if (upEndTime > beginTime)
                                    {
                                        beginTime = upEndTime;
                                    }
                                }

                                if (atm.getExpStartTime() != null && !"".equals(atm.getExpStartTime()))
                                {
                                    beginTime = Long.valueOf(atm.getExpStartTime());
                                }

                                if (atm.getExpEndTime() != null && !"".equals(atm.getExpEndTime()))
                                {
                                    endTime = Long.valueOf(atm.getExpEndTime());
                                } else
                                {
                                    endTime = beginTime + avgTime;
                                }
                            } else
                            {
                                String[] timeConfig = (ganttDelayerBean.getTimestr()).split("\\:");
                                long delayerTime = Long.valueOf("*".equals(timeConfig[3]) ? "0" : timeConfig[3]) * 60
                                        * 60 * 1000
                                        + Long.valueOf("*".equals(timeConfig[4]) ? "0" : timeConfig[4]) * 60 * 1000
                                        + Long.valueOf("*".equals(timeConfig[5]) ? "0" : timeConfig[5]) * 1000;
                                if (atm.getExpStartTime() != null && !"".equals(atm.getExpStartTime()))
                                {
                                    beginTime = Long.valueOf(atm.getExpStartTime());
                                } else
                                {
                                    if (ganttBeanParent != null)
                                    {
                                        beginTime = upEndTime + delayerTime;
                                    } else
                                    {
                                        beginTime = upEndTime + delayerTime + iacrossdayNum * 24 * 60 * 60 * 1000;
                                    }
                                }
                                if (atm.getExpEndTime() != null && !"".equals(atm.getExpEndTime()))
                                {
                                    endTime = Long.valueOf(atm.getExpEndTime());
                                } else
                                {
                                    endTime = beginTime + avgTime;
                                }
                            }
                        }

                        // ????????????????????????????????????
                        if (listParent != null && !listParent.isEmpty()) {
                            long maxParentEndTime = 0l;
                            for (Object parentId : listParent) {
                                GanttBean parentBean = (GanttBean) mapAct.get(parentId);
                                if (parentBean != null && parentBean.getExpEndTime() != null
                                    && !"".equals(parentBean.getExpEndTime())) {
                                    try {
                                        long parentEndTime = Long.parseLong(parentBean.getExpEndTime());
                                        if (parentEndTime > maxParentEndTime) {
                                            maxParentEndTime = parentEndTime;
                                        }
                                    } catch (NumberFormatException e) {
                                        _log.warn("Invalid parent expEndTime in getExpTime: " + parentBean.getExpEndTime());
                                    }
                                }
                            }

                            if (maxParentEndTime > 0 && beginTime < maxParentEndTime) {
                                _log.info("Adjusting start time in getExpTime for node " + ganttBean.getId() +
                                    " from " + beginTime + " to " + maxParentEndTime);
                                long duration = endTime - beginTime;
                                beginTime = maxParentEndTime;
                                endTime = beginTime + duration;
                            }
                        }

                        ganttBean.setExpStartTime(String.valueOf(beginTime));
                        ganttBean.setExpEndTime(String.valueOf(endTime));
                    } else if ("0".equals(ganttBean.getActState()))// ??????????????????
                    {
                        ganttBean.setExpStartTime(null);
                        beginTime = ganttBean.getRealBeginTime() == null || "".equals(ganttBean.getRealBeginTime()) ? 0l
                                : Long.valueOf(ganttBean.getRealBeginTime());
                        ganttBean.setExpStartTime((String.valueOf(beginTime)));
                        endTime = beginTime + avgTime;
                        if (nowTime > endTime)
                        {
                            endTime = nowTime;
                        }

                        if (atm.getExpEndTime() != null && !"".equals(atm.getExpEndTime()))
                        {
                            endTime = Long.valueOf(atm.getExpEndTime());
                        }

                        ganttBean.setExpEndTime(String.valueOf(endTime));

                        // ???????????????? add by wangnan -----start-----
                        // ?????????????
                        boolean avgTimeWarnningSwitch = ServerEnv.getInstance()
                                .getBooleanConfig("avgTimeWarnningSwitch", false);
                        if (avgTimeWarnningSwitch)
                        {
                            saveAvgTimeWarnning(ganttBean, avgTimeList);
                        }
                        // ???????????????? add by wangnan -----end-----

                    } else if ("2".equals(ganttBean.getActState()) || "3".equals(ganttBean.getActState())) // ?????????????????
                    {
                        ganttBean.setExpStartTime(null);
                        ganttBean.setExpEndTime(null);
                    }
                }
            }
        } catch (Exception e)
        {
            _log.info("ActTimeCalculateThread.getExpTime", e);
        }
        return ganttBean;
    }

    /**
     * 
     * @Title: saveAvgTimeWarnning
     * @Description: ????????????????
     * @param ganttBean
     * @param avgTimeList
     * @throws Exception
     * @author: wangnan
     * @date: 2017??9??27?? ????11:56:18
     */
    private void saveAvgTimeWarnning ( GanttBean ganttBean, List avgTimeList ) throws Exception
    {
        Connection con = null;
        try
        {
            String projcetName = ganttBean.getProjectName();
            String flowName = ganttBean.getFlowName();
            long avgTime = 0;
            // ???????????????????????????????
            for (Object object : avgTimeList)
            {
                AvgTimeList bean = (AvgTimeList) object;
                if (projcetName.equals(bean.getPrjName()) && flowName.equals(bean.getFlowName()))
                {
                    avgTime = bean.getAvgTime();
                }
            }
            // ?????????????
            if (avgTime > 0)
            {
                con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                // ????????
                long timeoutNum = BatchTimeOffsetManager.getInstance().getTimeoutNumByPrjNameAndActName(con,
                    projcetName, ganttBean.getActName());
                // ????????????????0????????????????????????
                if (timeoutNum == 0)
                {
                    timeoutNum = ServerEnv.getInstance().getLongConfig("defaultTimeoutNum", 50);
                }
                avgTime = avgTime + (avgTime * timeoutNum / 100);
                long beginTime = ganttBean.getRealBeginTime() == null || "".equals(ganttBean.getRealBeginTime()) ? 0
                        : Long.valueOf(ganttBean.getRealBeginTime());
                long endTime = System.currentTimeMillis();
                // ???????????????????? (??????+?????)??????????????????????
                if (beginTime > 0 && endTime - beginTime > avgTime)
                {
                    // ???????????????
                    long warnningCount = WatcherManager.getInstance().getWarnningCountByPrjAndFlowAndAct(con,
                        projcetName, flowName, ganttBean.getActName());
                    if (warnningCount == 0)
                    {
                        // ???actmesid
                        long actmesid = WatcherManager.getInstance().getActmesidByPrjAndFlowAndAct(con, projcetName,
                            flowName, ganttBean.getActName());
                        WatcherManager.getInstance().saveWarnningInfo(con, ganttBean, actmesid);
                    }
                }
            }
        } catch (Exception e)
        {
            _log.error(e);
        } finally
        {
            if (con != null)
            {
                con.close();
            }
        }
    }

    public void actWarn ( String dataDate, Map map, long nowTime, Connection con, Long realRunTime, Long realTime )
            throws Exception
    {
        try
        {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH点mm分ss秒");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
            // ?????
            if (getActTimeWarn())
            {
                // ????????
                List listWarnSetActOpenTime = SequenceManager.getInstance().getWarnSetActOpenTime(dataDate, con);
                if (listWarnSetActOpenTime != null && !listWarnSetActOpenTime.isEmpty())
                {
                    long a1 = System.currentTimeMillis();
                    for (int j = 0; j < listWarnSetActOpenTime.size(); j++)
                    {
                        GanttBean ganttBean = (GanttBean) listWarnSetActOpenTime.get(j);

                        String mainDataDate = SequenceManager.getInstance().getMainConfig(ganttBean.getProjectName(),
                            ganttBean.getFirstFlowName(), con);
                        long mainDataDateL = 0l;
                        long dataDateL = sdf.parse(dataDate).getTime();
                        if (mainDataDate != null && !"".equals(mainDataDate))
                        {
                            mainDataDateL = sdf2.parse(mainDataDate).getTime();
                        }

                        if (!"".contentEquals(ganttBean.getBusinessBeginTime())
                                && ganttBean.getBusinessBeginTime() != null && dataDateL >= mainDataDateL)
                        {
                            int day = 0;
                            if ("次日".equals(ganttBean.getBusinessBeginTimeType()))
                            {
                                day = 1;
                            }
                            long warningTime = sdf1.parse(dataDate.trim() + ' ' + ganttBean.getBusinessBeginTime())
                                    .getTime() + day * 24 * 60 * 60 * 1000;
                            // ?????
                            if (nowTime > warningTime)
                            {
                                Map mapWarn = SequenceManager.getInstance().getWarnAct(con, ganttBean.getId(), null,
                                    dataDate, ganttBean.getBusinessBeginTime(), 0l, "0");
                                if (mapWarn == null)
                                {
                                    SequenceManager.getInstance().insertWarnAct(con, ganttBean.getId(), null, dataDate,
                                        ganttBean.getBusinessBeginTime(), 0l, "0", "0", ganttBean);
                                    SequenceManager.getInstance().updateWarnAct(con, ganttBean.getId(), dataDate, "1");
                                } else
                                {
                                    if (Long.valueOf("0").equals((Long) mapWarn.get("iconfirmtype")))
                                    {
                                        SequenceManager.getInstance().updateWarnActSon(con, (Long) mapWarn.get("iid"));
                                        SequenceManager.getInstance().updateWarnAct(con, ganttBean.getId(), dataDate,
                                            "1");
                                    }
                                }
                            }
                            // ??????
                            if (Long.valueOf(
                                ganttBean.getExpEndTime() == null ? "0" : ganttBean.getExpEndTime()) > warningTime)
                            {
                                GanttBean ganttBeanExp = (GanttBean) map.get(ganttBean.getId());
                                if (null != ganttBeanExp)
                                {
                                    Map mapWarn = SequenceManager.getInstance().getWarnAct(con, ganttBean.getId(),
                                        ganttBeanExp.getSourceId(), dataDate, ganttBean.getBusinessBeginTime(), 0l,
                                        "1");
                                    if (mapWarn == null)
                                    {
                                        SequenceManager.getInstance().insertWarnAct(con, ganttBean.getId(),
                                            ganttBeanExp.getSourceId(), dataDate, ganttBean.getBusinessBeginTime(), 0l,
                                            "1", "0", ganttBean);
                                        SequenceManager.getInstance().updateWarnAct(con, ganttBean.getId(), dataDate,
                                            "2");
                                    } else
                                    {
                                        if (Long.valueOf("0").equals((Long) mapWarn.get("iconfirmtype")))
                                        {
                                            SequenceManager.getInstance().updateWarnActSon(con,
                                                (Long) mapWarn.get("iid"));
                                            SequenceManager.getInstance().updateWarnAct(con, ganttBean.getId(),
                                                dataDate, "2");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    long a2 = System.currentTimeMillis();
                    _log.info("????????:" + dataDate + " setActOpenTime size=" + listWarnSetActOpenTime.size() + " time="
                            + (a2 - a1));
                }

                // ?????????????
                List listWarnSetActArriveTime = SequenceManager.getInstance().getWarnSetActArriveTime(dataDate, con);
                if (listWarnSetActArriveTime != null && !listWarnSetActArriveTime.isEmpty())
                {
                    long a1 = System.currentTimeMillis();
                    for (int j = 0; j < listWarnSetActArriveTime.size(); j++)
                    {
                        GanttBean ganttBean = (GanttBean) listWarnSetActArriveTime.get(j);

                        String mainDataDate = SequenceManager.getInstance().getMainConfig(ganttBean.getProjectName(),
                            ganttBean.getFirstFlowName(), con);
                        long mainDataDateL = 0l;
                        long dataDateL = sdf.parse(dataDate).getTime();
                        if (mainDataDate != null && !"".equals(mainDataDate))
                        {
                            mainDataDateL = sdf2.parse(mainDataDate).getTime();
                        }

                        if (!"".contentEquals(ganttBean.getArriveTime()) && ganttBean.getArriveTime() != null
                                && dataDateL >= mainDataDateL)
                        {
                            int day = 0;
                            if ("????".equals(ganttBean.getArriveTimeType()))
                            {
                                day = 1;
                            }
                            long warningTime = sdf1.parse(dataDate + ' ' + ganttBean.getArriveTime()).getTime()
                                    + day * 24 * 60 * 60 * 1000;

                            // ?????
                            if (nowTime > warningTime)
                            {
                                Map mapWarn = SequenceManager.getInstance().getWarnAct(con, ganttBean.getId(), null,
                                    dataDate, ganttBean.getArriveTime(), 1l, "0");
                                if (mapWarn == null)
                                {
                                    SequenceManager.getInstance().insertWarnAct(con, ganttBean.getId(), null, dataDate,
                                        ganttBean.getArriveTime(), 1l, "0", "0", ganttBean);
                                    SequenceManager.getInstance().updateWarnAct(con, ganttBean.getId(), dataDate, "1");
                                } else
                                {
                                    if (Long.valueOf("0").equals((Long) mapWarn.get("iconfirmtype")))
                                    {
                                        SequenceManager.getInstance().updateWarnActSon(con, (Long) mapWarn.get("iid"));
                                        SequenceManager.getInstance().updateWarnAct(con, ganttBean.getId(), dataDate,
                                            "1");
                                    }
                                }
                            }

                            // ??????
                            if (Long.valueOf(ganttBean.getExpEndTime() == null ? "0" : ganttBean.getExpEndTime())
                                    + realTime > warningTime)
                            {
                                GanttBean ganttBeanExp = (GanttBean) map.get(ganttBean.getId());
                                if (null != ganttBeanExp)
                                {
                                    Map mapWarn = SequenceManager.getInstance().getWarnAct(con, ganttBean.getId(),
                                        ganttBeanExp.getSourceId(), dataDate, ganttBean.getArriveTime(), 1l, "1");
                                    if (mapWarn == null)
                                    {
                                        SequenceManager.getInstance().insertWarnAct(con, ganttBean.getId(),
                                            ganttBeanExp.getSourceId(), dataDate, ganttBean.getArriveTime(), 1l, "1",
                                            "0", ganttBean);
                                        SequenceManager.getInstance().updateWarnAct(con, ganttBean.getId(), dataDate,
                                            "2");
                                    } else
                                    {
                                        if (Long.valueOf("0").equals((Long) mapWarn.get("iconfirmtype")))
                                        {
                                            SequenceManager.getInstance().updateWarnActSon(con,
                                                (Long) mapWarn.get("iid"));
                                            SequenceManager.getInstance().updateWarnAct(con, ganttBean.getId(),
                                                dataDate, "2");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    long a2 = System.currentTimeMillis();
                    _log.info("????????:" + dataDate + " setActArriveTime size=" + listWarnSetActArriveTime.size()
                            + " time=" + (a2 - a1));
                }
            }
        } catch (Exception e)
        {
            _log.error(e);
            throw e;
        }
    }

    // ??????????
    public boolean validTime ( GanttValTimeBean ganttValTimeBean, Date mainFlowStartDate )
    {

        boolean flag = true;

        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(mainFlowStartDate.getTime());
        int months = cal.get(Calendar.MONTH);
        int week = (cal.get(Calendar.DAY_OF_WEEK) == 1 ? 8 : cal.get(Calendar.DAY_OF_WEEK)) - 2;
        int day = cal.get(Calendar.DATE);
        if (ganttValTimeBean == null)
        {
            return flag;
        }
        // ??????
        if (Long.valueOf("0").equals(ganttValTimeBean.getMonthall()))
        {
            if (Long.valueOf("1").equals(ganttValTimeBean.getMonthexc()))
            {
                flag = true;
            } else
            {
                flag = false;
            }
            String monthsString = ganttValTimeBean.getMonth();
            if (monthsString != null && !"".equals(monthsString))
            {
                String[] monthsStrings = monthsString.split(",");
                for (int i = 0; i < monthsStrings.length; i++)
                {
                    if (monthsStrings[i].equals(String.valueOf(months)))
                    {
                        flag = !Long.valueOf("1").equals(ganttValTimeBean.getMonthexc());
                        break;
                    }
                }
            }
            if (!flag)
            {
                return flag;
            }
        }
        // ??????
        if (Long.valueOf("0").equals(ganttValTimeBean.getWeekdayall()))
        {
            if (Long.valueOf("1").equals(ganttValTimeBean.getWeekdayexc()))
            {
                flag = true;
            } else
            {
                flag = false;
            }
            String weekString = ganttValTimeBean.getWeek();
            if (weekString != null && !"".equals(weekString))
            {
                String[] weekStrings = weekString.split(",");
                for (int i = 0; i < weekStrings.length; i++)
                {
                    if (weekStrings[i].equals(String.valueOf(week)))
                    {
                        flag = !Long.valueOf("1").equals(ganttValTimeBean.getWeekdayexc());
                        break;
                    }
                }
            }
            if (!flag)
            {
                return flag;
            }
        }
        // ??????
        if (Long.valueOf("0").equals(ganttValTimeBean.getIsdayall()))
        {
            if (Long.valueOf("1").equals(ganttValTimeBean.getDayexc()))
            {
                flag = true;
            } else
            {
                flag = false;
            }
            String dayString = ganttValTimeBean.getDay();
            if (dayString != null && !"".equals(dayString))
            {
                String[] dayStrings = dayString.split(",");
                for (int i = 0; i < dayStrings.length; i++)
                {
                    int dayInt = Integer.parseInt(dayStrings[i]);
                    if (dayInt < 0)
                    {
                        // ????
                        if ((cal.getActualMaximum(Calendar.DATE) + 1 + dayInt) == day)
                        {
                            flag = !Long.valueOf("1").equals(ganttValTimeBean.getDayexc());
                            break;
                        }
                    } else
                    {
                        // ????
                        if (dayInt == day)
                        {
                            flag = !Long.valueOf("1").equals(ganttValTimeBean.getDayexc());
                            break;
                        }
                    }
                }
            }
            if (!flag)
            {
                return flag;
            }
        }

        return flag;
    }

    // ????????? - ???????????????????
    public boolean isExsitAct ( List list, Map map )
    {
        boolean flag = true;
        if (list != null && !list.isEmpty())
        {
            for (int i = 0; i < list.size(); i++)
            {
                String id = (String) list.get(i);
                GanttBean ganttBean = (GanttBean) map.get(id);
                if (ganttBean == null)
                {
                    flag = false;
                    _log.debug("Parent node not found in mapAct: " + id);
                    break;
                }

                // ??????????????????????????
                if (ganttBean.getExpEndTime() == null || "".equals(ganttBean.getExpEndTime()) || "0".equals(ganttBean.getExpEndTime()))
                {
                    flag = false;
                    _log.debug("Parent node has no valid expEndTime: " + id + ", expEndTime: " + ganttBean.getExpEndTime());
                    break;
                }

                // ????????????????????
                if ("1".equals(ganttBean.getIsProOut()))
                {
                    // ?????????????????????????
                    if (ganttBean.getExpStartTime() == null || "".equals(ganttBean.getExpStartTime()) || "0".equals(ganttBean.getExpStartTime()))
                    {
                        flag = false;
                        _log.debug("Cross-system parent node has no valid expStartTime: " + id);
                        break;
                    }
                }
            }
        }

        return flag;
    }

    /**
     * ?????????????????
     * @param listParent ?????????
     * @param mapAct ?????
     * @return ?????????
     */
    private long calculateMaxParentEndTime(List listParent, Map mapAct) {
        long maxEndTime = 0l;
        if (listParent != null && !listParent.isEmpty()) {
            for (Object id : listParent) {
                GanttBean parentBean = (GanttBean) mapAct.get(id);
                if (parentBean != null && parentBean.getExpEndTime() != null
                    && !"".equals(parentBean.getExpEndTime())) {
                    try {
                        long parentEndTime = Long.parseLong(parentBean.getExpEndTime());
                        if (parentEndTime > maxEndTime) {
                            maxEndTime = parentEndTime;
                        }
                    } catch (NumberFormatException e) {
                        _log.warn("Invalid expEndTime format for parent node: " + id + ", value: " + parentBean.getExpEndTime());
                    }
                }
            }
        }
        return maxEndTime;
    }

    /**
     * ???????????????????????????????????????????????
     * @param ganttBean ??????
     * @param listParent ?????????
     * @param mapAct ?????
     */
    private void validateAndFixStartTime(GanttBean ganttBean, List listParent, Map mapAct) {
        if (ganttBean == null || ganttBean.getExpStartTime() == null || "".equals(ganttBean.getExpStartTime())) {
            return;
        }

        long maxParentEndTime = calculateMaxParentEndTime(listParent, mapAct);
        if (maxParentEndTime > 0) {
            try {
                long currentStartTime = Long.parseLong(ganttBean.getExpStartTime());
                if (currentStartTime < maxParentEndTime) {
                    _log.info("Adjusting start time for node " + ganttBean.getId() +
                        " from " + currentStartTime + " to " + maxParentEndTime +
                        " to ensure it's not earlier than parent end time");

                    // ??????????
                    ganttBean.setExpStartTime(String.valueOf(maxParentEndTime));

                    // ??????????????????????????
                    if (ganttBean.getExpEndTime() != null && !"".equals(ganttBean.getExpEndTime())) {
                        try {
                            long currentEndTime = Long.parseLong(ganttBean.getExpEndTime());
                            long duration = currentEndTime - currentStartTime;
                            if (duration > 0) {
                                ganttBean.setExpEndTime(String.valueOf(maxParentEndTime + duration));
                            }
                        } catch (NumberFormatException e) {
                            _log.warn("Invalid expEndTime format for node: " + ganttBean.getId() + ", value: " + ganttBean.getExpEndTime());
                        }
                    }
                }
            } catch (NumberFormatException e) {
                _log.warn("Invalid expStartTime format for node: " + ganttBean.getId() + ", value: " + ganttBean.getExpStartTime());
            }
        }
    }

    /**
     * ??????????????????
     * @param nodeId ???ID
     * @param dependencyMap ??????????
     * @param mapAct ?????
     * @param list ?????????
     */
    private void triggerDependentNodesRecalculation(String nodeId, Map<String, List<String>> dependencyMap,
                                                   Map mapAct, List list) {
        if (dependencyMap.containsKey(nodeId)) {
            List<String> dependentNodes = dependencyMap.get(nodeId);
            for (String depId : dependentNodes) {
                GanttBean depNode = (GanttBean) mapAct.remove(depId); // ??????
                if (depNode != null) {
                    list.add(depNode); // ??????????????????
                    _log.debug("Triggered recalculation for dependent node: " + depId + " due to change in parent: " + nodeId);
                }
            }
        }
    }

    /**
     * ????????????????????
     * @param ganttBean ??????
     * @param listParent ?????????
     * @param mapAct ?????
     * @param dataDate ????????
     * @param con ?????????
     */
    private void ensureCrossSystemParentsCalculated(GanttBean ganttBean, List listParent, Map mapAct,
                                                   String dataDate, Connection con) {
        if (listParent == null || listParent.isEmpty()) {
            return;
        }

        try {
            for (Object parentIdObj : listParent) {
                String parentId = (String) parentIdObj;
                GanttBean parentBean = (GanttBean) mapAct.get(parentId);

                if (parentBean == null) {
                    // ?????????????????????
                    parentBean = loadCrossSystemParentNode(parentId, dataDate, con);
                    if (parentBean != null) {
                        mapAct.put(parentId, parentBean);
                        _log.info("Loaded cross-system parent node: " + parentId + " for child: " + ganttBean.getId());
                    } else {
                        _log.warn("Cannot find cross-system parent node: " + parentId + " for child: " + ganttBean.getId());
                        continue;
                    }
                }

                // ??????????????????
                if (parentBean.getExpEndTime() == null || "".equals(parentBean.getExpEndTime()) || "0".equals(parentBean.getExpEndTime())) {
                    _log.warn("Cross-system parent node has invalid expEndTime: " + parentId +
                        ", expEndTime: " + parentBean.getExpEndTime() + " for child: " + ganttBean.getId());
                }
            }
        } catch (Exception e) {
            _log.error("Error ensuring cross-system parents calculated for node: " + ganttBean.getId(), e);
        }
    }

    /**
     * ??????????????????
     * @param parentId ?????ID
     * @param dataDate ????????
     * @param con ?????????
     * @return ????????
     */
    private GanttBean loadCrossSystemParentNode(String parentId, String dataDate, Connection con) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT iid, idatadate, iprojectname, imainflowname, ifirstflowname, iflowname, " +
                        "iactname, iactdes, iactstate, irealbegintime, irealendtime, iexpstarttime, iexpendtime, " +
                        "iavgtime, iacrossday_num, iparallelflag, iissynccall, iisproout " +
                        "FROM ieai_act_topo_instance WHERE iid = ? AND idatadate = ?";

            ps = con.prepareStatement(sql);
            ps.setLong(1, Long.valueOf(parentId));
            ps.setString(2, dataDate);
            rs = ps.executeQuery();

            if (rs.next()) {
                GanttBean parentBean = new GanttBean();
                parentBean.setId(rs.getString("iid"));
                parentBean.setDataDate(rs.getString("idatadate"));
                parentBean.setProjectName(rs.getString("iprojectname"));
                parentBean.setMainFLowName(rs.getString("imainflowname"));
                parentBean.setFirstFlowName(rs.getString("ifirstflowname"));
                parentBean.setFlowName(rs.getString("iflowname"));
                parentBean.setActName(rs.getString("iactname"));
                parentBean.setActDes(rs.getString("iactdes"));
                parentBean.setActState(rs.getString("iactstate"));
                parentBean.setRealBeginTime(rs.getString("irealbegintime"));
                parentBean.setRealEndTime(rs.getString("irealendtime"));
                parentBean.setExpStartTime(rs.getString("iexpstarttime"));
                parentBean.setExpEndTime(rs.getString("iexpendtime"));
                parentBean.setAvgTime(rs.getString("iavgtime"));
                parentBean.setAcrossdayNum(rs.getString("iacrossday_num"));
                parentBean.setParallelFlag(rs.getString("iparallelflag"));
                parentBean.setIsSyncCall(rs.getString("iissynccall"));
                parentBean.setIsProOut(rs.getString("iisproout"));

                return parentBean;
            }
        } catch (Exception e) {
            _log.error("Error loading cross-system parent node: " + parentId, e);
        } finally {
            if (rs != null) {
                try { rs.close(); } catch (Exception e) { _log.error("Error closing ResultSet", e); }
            }
            if (ps != null) {
                try { ps.close(); } catch (Exception e) { _log.error("Error closing PreparedStatement", e); }
            }
        }
        return null;
    }

    // ????
    public Date getDateBefore ( Date d, int day )
    {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) - day);
        return now.getTime();
    }

    // ?????
    public Date getDateAfter ( Date d, int day )
    {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + day);
        return now.getTime();
    }

    // ???????????
    public boolean getActTimeCalculateThreadSwitch ()
    {
        boolean flag = true;
        if ("false".equals(Environment.getInstance().getConfig("actTimeCalculateThreadSwitch")))
        {
            flag = false;
        }
        return flag;
    }

    // ????????
    public boolean getActTimeWarn ()
    {
        boolean flag = true;
        if ("false".equals(Environment.getInstance().getConfig("actTimeWarn")))
        {
            flag = false;
        }
        return flag;
    }

    // ??????????
    public long getActTimeCalculateThreadInterval ()
    {
        long intervalTime;
        if (Environment.getInstance().getConfig("actTimeCalculateThreadInterval") == null
                || "".equals(Environment.getInstance().getConfig("actTimeCalculateThreadInterval")))
        {
            intervalTime = 300000;
        } else
        {
            try
            {
                intervalTime = Long.valueOf(Environment.getInstance().getConfig("actTimeCalculateThreadInterval"));
            } catch (Exception e)
            {
                intervalTime = 300000;
            }
        }
        return intervalTime;
    }

    public void delActInstanceIntoMess ( Map<String, Map<String, Map<String, String>>> data )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String insert = " insert into ieai_act_relation_mess(IID,IPROJECTNAME,IACTNAME,IACTDES,IACTSTATE,IAGENTIP,IFLOWSTATE,ibatchname,ibatchpath,IFLOWNAME) values(?,?,?,?,1,?,0,?,?,?)";
        String update = " update ieai_act_relation_mess set IACTDES=?,IAGENTIP=?,ibatchname=?,ibatchpath=? where IID=?";
        Map<String, String> relation = new HashMap<String, String>();
        Connection con = null;
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            for (Map.Entry<String, Map<String, Map<String, String>>> entry : data.entrySet())
            {
                PreparedStatement ps1 = null;
                try
                {
                    String prjKey = entry.getKey();
                    Map<String, Map<String, String>> prjValue = entry.getValue();

                    String sql = " delete from ieai_act_relation_mess where iid not in (select m.iid from ieai_act_relation_mess m,ieai_excelmodel n where m.iprojectname=n.ichildproname and m.iactname=n.iactname and n.ichildproname=?) AND iprojectname=?";
                    ps1 = con.prepareStatement(sql);
                    ps1.setString(1, prjKey);
                    ps1.setString(2, prjKey);
                    ps1.executeUpdate();
                    for (Map.Entry<String, Map<String, String>> entry1 : prjValue.entrySet())
                    {
                        PreparedStatement ps = null;
                        try
                        {
                            Map<String, String> idValue = entry1.getValue();
                            long saveOrUpdate = Long.parseLong(idValue.get("DATASTATE"));
                            String operationId = idValue.get("IOPERATIONID");
                            String agent = idValue.get("IAGENTSOURCEGROUP");
                            String prjName = idValue.get("ICHILDPRONAME");
                            String actName = idValue.get("IACTNAME");
                            String actDes = idValue.get("IACTDESCRIPTION");
                            String startShell = idValue.get("ISHELLHOUSE");
                            String shell = idValue.get("ISHELLABSOLUTEPATH");
                            String newId = idValue.get("DATASTATE");
                            if (saveOrUpdate == 0)
                            {
                                // insert
                                long mesId = IdGenerator.createId("ieai_act_relation_mess", con);
                                newId = String.valueOf(mesId);
                                ps = con.prepareStatement(insert);
                                ps.setLong(1, mesId);
                                ps.setString(2, prjName);
                                ps.setString(3, actName);
                                ps.setString(4, actDes);
                                ps.setString(5, agent);
                                ps.setString(6, startShell);
                                ps.setString(7, shell);
                                ps.setString(8, actName);
                                ps.executeUpdate();
                            } else
                            {
                                // update
                                ps = con.prepareStatement(update);
                                ps.setString(1, actDes);
                                ps.setString(2, agent);
                                ps.setString(3, startShell);
                                ps.setString(4, shell);
                                ps.setLong(5, saveOrUpdate);
                                ps.executeUpdate();
                            }
                            relation.put(operationId, newId);
                        } catch (Exception e)
                        {
                            _log.error(method, e);
                        } finally
                        {
                            DBResource.closePreparedStatement(ps, method, _log);
                        }
                    }
                } catch (Exception e)
                {
                    _log.error(method, e);
                } finally
                {
                    DBResource.closePreparedStatement(ps1, method, _log);
                }
            }
            con.commit();
        } catch (Exception tt)
        {
            _log.error(method + " error and next! ErrorInfo:", tt);
        } finally
        {
            DBResource.closeConnection(con, method, _log);
        }
    }
}
